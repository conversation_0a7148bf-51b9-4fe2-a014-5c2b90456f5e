<?php

declare(strict_types=1);

use App\Enums\VendorType;
use App\Models\Clinic;
use App\Models\ProductOffer;
use App\Models\Vendor;
use App\Modules\Account\Models\ClinicAccount;
use App\Modules\Gpo\Models\GpoAccount;
use App\Services\Sorting\ProductOfferSorter;

beforeEach(function () {
    $this->mwiVendor = Vendor::factory()->state([
        'id' => '9d7559b3-0737-464e-91ba-739b63ec41cd',
        'type' => VendorType::Distributor,
    ])->create();

    $this->otherDistributor = Vendor::factory()->state([
        'type' => VendorType::Distributor,
    ])->create();

    $this->manufacturer = Vendor::factory()->state([
        'type' => VendorType::Manufacturer,
    ])->create();

    // Create a clinic for testing
    $this->clinicAccount = ClinicAccount::factory()->create();
    $this->clinic = Clinic::factory()->create([
        'clinic_account_id' => $this->clinicAccount->id,
    ]);
});

test('independent clinic: sorts by price ascending', function () {
    $offers = collect([
        ProductOffer::factory()->create([
            'vendor_id' => $this->manufacturer->id,
            'price' => 100,
        ]),
        ProductOffer::factory()->create([
            'vendor_id' => $this->mwiVendor->id,
            'price' => 150,
        ]),
        ProductOffer::factory()->create([
            'vendor_id' => $this->otherDistributor->id,
            'price' => 50,
        ]),
    ]);

    $sortedOffers = ProductOfferSorter::sort($offers, $this->clinic->id);

    expect($sortedOffers->pluck('price')->toArray())->toBe([50, 100, 150]);
});

test('independent clinic: sorts by clinic price if present, otherwise by price', function () {
    $offer1 = ProductOffer::factory()->create([
        'vendor_id' => $this->manufacturer->id,
        'price' => 100,
    ]);
    $offer2 = ProductOffer::factory()->create([
        'vendor_id' => $this->mwiVendor->id,
        'price' => 150,
    ]);
    $offer3 = ProductOffer::factory()->create([
        'vendor_id' => $this->otherDistributor->id,
        'price' => 50,
    ]);

    $offer2->clinics()->attach($this->clinic->id, ['price' => 25]);

    $offers = collect([$offer1, $offer2, $offer3]);
    $sortedOffers = ProductOfferSorter::sort($offers, $this->clinic->id);

    expect($sortedOffers->pluck('id')->toArray())->toBe([
        $offer2->id, // clinic price 25
        $offer3->id, // price 50
        $offer1->id, // price 100
    ]);
});

test('GPO clinic: recommended vendors sorted by order, then non-recommended vendors after sorted by price asc', function () {
    $gpo = GpoAccount::factory()->create();
    $this->clinicAccount->update(['gpo_account_id' => $gpo->id]);

    // Attach recommended vendors with order
    $gpo->recommendedVendors()->attach($this->otherDistributor->id, ['order' => 0]);
    $gpo->recommendedVendors()->attach($this->mwiVendor->id, ['order' => 1]);

    $otherManufacturer = Vendor::factory()->state([
        'type' => VendorType::Manufacturer,
    ])->create();

    // Offers: both recommended and non-recommended
    $offers = collect([
        $offer1 = ProductOffer::factory()->create([
            'vendor_id' => $this->mwiVendor->id,
            'price' => 200,
        ]),
        $offer2 = ProductOffer::factory()->create([
            'vendor_id' => $this->manufacturer->id,
            'price' => 50,
        ]),
        $offer3 = ProductOffer::factory()->create([
            'vendor_id' => $otherManufacturer->id,
            'price' => 100,
        ]),
        $offer4 = ProductOffer::factory()->create([
            'vendor_id' => $this->otherDistributor->id,
            'price' => 150,
        ]),
    ]);

    $sortedOffers = ProductOfferSorter::sort($offers, $this->clinic->id);

    expect($sortedOffers->pluck('id')->toArray())->toBe([
        $offer4->id, // otherDistributor, order 0, price 150
        $offer1->id, // mwiVendor, order 1, price 200
        $offer2->id, // manufacturer, not recommended, price 50
        $offer3->id, // otherManufacturer, not recommended, price 100
    ]);
});

test('GPO clinic: non-recommended vendors sorted by price after recommended', function () {
    $gpo = GpoAccount::factory()->create();
    $this->clinicAccount->update(['gpo_account_id' => $gpo->id]);
    $gpo->recommendedVendors()->attach($this->mwiVendor->id, ['order' => 0]);

    $offers = collect([
        $offer1 = ProductOffer::factory()->create([
            'vendor_id' => $this->manufacturer->id,
            'price' => 200,
        ]),
        $offer2 = ProductOffer::factory()->create([
            'vendor_id' => $this->mwiVendor->id,
            'price' => 100,
        ]),
        $offer3 = ProductOffer::factory()->create([
            'vendor_id' => $this->otherDistributor->id,
            'price' => 50,
        ]),
    ]);

    $sortedOffers = ProductOfferSorter::sort($offers, $this->clinic->id);

    expect($sortedOffers->pluck('id')->toArray())->toBe([
        $offer2->id, // mwiVendor, recommended
        $offer3->id, // manufacturer, not recommended, price 50
        $offer1->id, // manufacturer, not recommended, price 200
    ]);
});
