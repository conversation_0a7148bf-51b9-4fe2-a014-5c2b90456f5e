<?php

declare(strict_types=1);

use App\Enums\VendorAuthenticationKind;
use App\Models\Clinic;
use App\Models\User;
use App\Models\Vendor;
use App\Modules\Account\Models\ClinicAccount;
use App\Modules\CatalogSync\Enums\CatalogSyncTaskStatus;
use App\Modules\CatalogSync\Models\CatalogSyncTask;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Integration\Models\IntegrationConnection;
use Illuminate\Support\Facades\Storage;
use Illuminate\Testing\TestResponse;

function getClinicVendorConnections(string $clinicId): TestResponse
{
    return test()->getJson("/api/clinics/{$clinicId}/vendors");
}

beforeEach(function () {
    $this->user = User::factory()->create();

    $this->account = ClinicAccount::factory()->create();
    $this->user->update([
        'account_id' => $this->account->id,
    ]);

    $this->clinic = Clinic::factory()->create([
        'clinic_account_id' => $this->account->id,
    ]);
});

describe('get clinic vendor connections', function () {
    it('returns an empty list when the clinic has no vendor connections', function () {
        $this->actingAs($this->user);

        getClinicVendorConnections($this->clinic->id)
            ->assertOk()
            ->assertJsonCount(0)
            ->assertJson([]);
    });

    it('returns the connection status for each vendor', function (IntegrationConnectionStatus $status) {
        $this->actingAs($this->user)->withHeader('highfive-clinic', $this->clinic->id);

        $vendor = Vendor::factory()->enabled()->create(['image_path' => 'vendor-images/test.png']);

        IntegrationConnection::create([
            'clinic_id' => $this->clinic->id,
            'vendor_id' => $vendor->id,
            'status' => $status,
            'credentials' => [],
        ]);

        Storage::fake('local', ['url' => config('app.url')]);

        getClinicVendorConnections($this->clinic->id)
            ->assertOk()
            ->assertJsonCount(1)
            ->assertJson([
                [
                    'id' => $vendor->id,
                    'imageUrl' => config('app.url').'/storage/vendor-images/test.png',
                    'status' => $status->value,
                    'authenticationKind' => VendorAuthenticationKind::Basic->value,
                    'authenticationConfiguration' => null,
                ],
            ]);
    })->with(IntegrationConnectionStatus::cases());

    it('returns the shipping terms for each vendor', function () {
        $this->actingAs($this->user)->withHeader('highfive-clinic', $this->clinic->id);
        $vendor = Vendor::factory()->enabled()->create(['image_path' => 'vendor-images/test.png']);

        $vendor->shippingTerms()->create([
            'cutoff_time' => '5pm local time',
            'free_shipping_threshold' => 0,
            'shipping_rate' => 0,
        ]);

        getClinicVendorConnections($this->clinic->id)
            ->assertOk()
            ->assertJsonCount(1)
            ->assertJson([
                [
                    'id' => $vendor->id,
                    'shippingTerms' => [
                        'cutoffTime' => '5pm local time',
                        'freeShippingThreshold' => '0.00',
                        'shippingRate' => '0.00',
                        'shippingType' => null,
                    ],
                ],
            ]);
    });

    it('returns last product catalog sync', function () {
        $this->actingAs($this->user)->withHeader('highfive-clinic', $this->clinic->id);

        $vendor = Vendor::factory()->enabled()->create(['image_path' => 'vendor-images/test.png']);

        $connection = IntegrationConnection::create([
            'clinic_id' => $this->clinic->id,
            'vendor_id' => $vendor->id,
            'status' => IntegrationConnectionStatus::Connected,
            'credentials' => [],
        ]);

        Storage::fake('local', ['url' => config('app.url')]);

        $task = CatalogSyncTask::factory()->create([
            'integration_connection_id' => $connection->id,
            'status' => CatalogSyncTaskStatus::Succeeded,
        ]);

        getClinicVendorConnections($this->clinic->id)
            ->assertOk()
            ->assertJsonCount(1)
            ->assertJson([
                [
                    'id' => $vendor->id,
                    'lastProductCatalogSync' => [
                        'id' => $task->id,
                        'status' => CatalogSyncTaskStatus::Succeeded->value,
                    ],
                ],
            ]);
    });
});
