<?php

declare(strict_types=1);

use App\Enums\ShippingType;
use App\Models\Clinic;
use App\Models\Vendor;
use App\Models\VendorClinicShippingSetting;

test('vendor clinic shipping setting can be created', function () {
    $vendor = Vendor::factory()->create();
    $clinic = Clinic::factory()->create();

    $shippingSetting = VendorClinicShippingSetting::factory()->create([
        'vendor_id' => $vendor->id,
        'clinic_id' => $clinic->id,
        'shipping_type' => ShippingType::FlatFee,
        'default_shipping_fee' => 1500, // $15.00 in cents
    ]);

    expect($shippingSetting->vendor_id)->toBe($vendor->id);
    expect($shippingSetting->clinic_id)->toBe($clinic->id);
    expect($shippingSetting->shipping_type)->toBe(ShippingType::FlatFee);
    expect($shippingSetting->default_shipping_fee)->toBe(1500);
});

test('shipping setting belongs to vendor and clinic', function () {
    $vendor = Vendor::factory()->create();
    $clinic = Clinic::factory()->create();
    $shippingSetting = VendorClinicShippingSetting::factory()->create([
        'vendor_id' => $vendor->id,
        'clinic_id' => $clinic->id,
    ]);

    expect($shippingSetting->vendor->id)->toBe($vendor->id);
    expect($shippingSetting->clinic->id)->toBe($clinic->id);
});

test('unique constraint prevents duplicate vendor clinic combinations', function () {
    $vendor = Vendor::factory()->create();
    $clinic = Clinic::factory()->create();

    VendorClinicShippingSetting::factory()->create([
        'vendor_id' => $vendor->id,
        'clinic_id' => $clinic->id,
    ]);

    expect(fn () => VendorClinicShippingSetting::factory()->create([
        'vendor_id' => $vendor->id,
        'clinic_id' => $clinic->id,
    ]))->toThrow(Illuminate\Database\QueryException::class);
});

describe('shipping fee calculation', function () {
    test('always free shipping returns zero cost', function () {
        $shippingSetting = VendorClinicShippingSetting::factory()->alwaysFree()->create();

        [$amountNeeded, $shippingFee] = $shippingSetting->calculateShippingFee(10000); // $100.00

        expect($amountNeeded)->toBe(0);
        expect($shippingFee)->toBe(0);
    });

    test('flat fee shipping returns fixed cost', function () {
        $shippingSetting = VendorClinicShippingSetting::factory()->flatFee(1500)->create(); // $15.00 in cents

        [$amountNeeded, $shippingFee] = $shippingSetting->calculateShippingFee(5000); // $50.00

        expect($amountNeeded)->toBe(0);
        expect($shippingFee)->toBe(1500); // $15.00 in cents
    });

    test('free over minimum when threshold is met returns zero cost', function () {
        $shippingSetting = VendorClinicShippingSetting::factory()->freeOverMinimum(10000, 1500)->create(); // $100.00 threshold, $15.00 fee in cents

        [$amountNeeded, $shippingFee] = $shippingSetting->calculateShippingFee(15000); // $150.00

        expect($amountNeeded)->toBe(0);
        expect($shippingFee)->toBe(0);
    });

    test('free over minimum when threshold is not met returns amount needed', function () {
        $shippingSetting = VendorClinicShippingSetting::factory()->freeOverMinimum(10000, 1500)->create(); // $100.00 threshold, $15.00 fee in cents

        [$amountNeeded, $shippingFee] = $shippingSetting->calculateShippingFee(7500); // $75.00

        expect($amountNeeded)->toBe(2500); // Need $25.00 more
        expect($shippingFee)->toBe(1500); // $15.00 in cents
    });
});

describe('factory states', function () {
    test('flat fee factory state creates correct shipping setting', function () {
        $shippingSetting = VendorClinicShippingSetting::factory()->flatFee(2000)->create(); // $20.00 in cents

        expect($shippingSetting->shipping_type)->toBe(ShippingType::FlatFee);
        expect($shippingSetting->default_shipping_fee)->toBe(2000); // $20.00 in cents
        expect($shippingSetting->free_shipping_threshold)->toBeNull();
    });

    test('free over minimum factory state creates correct shipping setting', function () {
        $shippingSetting = VendorClinicShippingSetting::factory()->freeOverMinimum(15000, 1500)->create(); // $150.00 threshold, $15.00 fee in cents

        expect($shippingSetting->shipping_type)->toBe(ShippingType::FreeOverMinimum);
        expect($shippingSetting->default_shipping_fee)->toBe(1500); // $15.00 in cents
        expect($shippingSetting->free_shipping_threshold)->toBe(15000); // $150.00 in cents
    });

    test('always free factory state creates correct shipping setting', function () {
        $shippingSetting = VendorClinicShippingSetting::factory()->alwaysFree()->create();

        expect($shippingSetting->shipping_type)->toBe(ShippingType::AlwaysFree);
        expect($shippingSetting->default_shipping_fee)->toBeNull();
        expect($shippingSetting->free_shipping_threshold)->toBeNull();
    });
});

describe('vendor shipping fallback system', function () {
    test('vendor uses clinic-specific settings when available', function () {
        $vendor = Vendor::factory()->create();
        $clinic = Clinic::factory()->create();

        // Create vendor-level shipping terms
        $vendor->shippingTerms()->create([
            'free_shipping_threshold' => 10000, // $100.00 in cents
            'shipping_rate' => 1500, // $15.00 in cents
        ]);

        // Create clinic-specific settings that override vendor defaults
        VendorClinicShippingSetting::factory()->flatFee(2500)->create([ // $25.00 in cents
            'vendor_id' => $vendor->id,
            'clinic_id' => $clinic->id,
        ]);

        $shippingTerms = $vendor->getShippingTerms($clinic);

        expect($shippingTerms)->toBeInstanceOf(VendorClinicShippingSetting::class);
        expect($shippingTerms->shipping_type)->toBe(ShippingType::FlatFee);
        expect($shippingTerms->default_shipping_fee)->toBe(2500); // $25.00 in cents
    });

    test('vendor falls back to vendor-level settings when no clinic-specific settings exist', function () {
        $vendor = Vendor::factory()->create();
        $clinic = Clinic::factory()->create();

        // Only create vendor-level shipping terms
        $vendorShippingTerms = $vendor->shippingTerms()->create([
            'free_shipping_threshold' => 10000, // $100.00 in cents
            'shipping_rate' => 1500, // $15.00 in cents
        ]);

        $shippingTerms = $vendor->getShippingTerms($clinic);

        expect($shippingTerms)->toBeInstanceOf(App\Models\VendorShippingTerms::class);
        expect($shippingTerms->id)->toBe($vendorShippingTerms->id);
        expect($shippingTerms->free_shipping_threshold)->toBe(10000);
        expect($shippingTerms->shipping_rate)->toBe(1500);
    });

    test('vendor returns null when no shipping settings exist', function () {
        $vendor = Vendor::factory()->create();
        $clinic = Clinic::factory()->create();

        $shippingTerms = $vendor->getShippingTerms($clinic);

        expect($shippingTerms)->toBeNull();
    });

    test('calculateShippingFee uses clinic-specific settings', function () {
        $vendor = Vendor::factory()->create();
        $clinic = Clinic::factory()->create();

        // Create vendor-level settings
        $vendor->shippingTerms()->create([
            'free_shipping_threshold' => 10000, // $100.00
            'shipping_rate' => 1500, // $15.00
        ]);

        // Create clinic-specific settings with always free shipping
        VendorClinicShippingSetting::factory()->alwaysFree()->create([
            'vendor_id' => $vendor->id,
            'clinic_id' => $clinic->id,
        ]);

        [$amountNeeded, $shippingFee] = $vendor->calculateShippingFee($clinic, 5000); // $50.00

        // Should use clinic-specific "always free" instead of vendor's $15 fee
        expect($amountNeeded)->toBe(0);
        expect($shippingFee)->toBe(0);
    });

    test('calculateShippingFee falls back to vendor settings', function () {
        $vendor = Vendor::factory()->create();
        $clinic = Clinic::factory()->create();

        // Only create vendor-level settings
        $vendor->shippingTerms()->create([
            'free_shipping_threshold' => 10000, // $100.00
            'shipping_rate' => 1500, // $15.00
        ]);

        [$amountNeeded, $shippingFee] = $vendor->calculateShippingFee($clinic, 5000); // $50.00

        // Should use vendor settings: need $50 more for free shipping, otherwise $15 fee
        expect($amountNeeded)->toBe(5000); // Need $50.00 more
        expect($shippingFee)->toBe(1500); // $15.00 shipping fee
    });

    test('calculateShippingFee returns zero when no settings exist', function () {
        $vendor = Vendor::factory()->create();
        $clinic = Clinic::factory()->create();

        [$amountNeeded, $shippingFee] = $vendor->calculateShippingFee($clinic, 5000);

        expect($amountNeeded)->toBe(0);
        expect($shippingFee)->toBe(0);
    });
});
