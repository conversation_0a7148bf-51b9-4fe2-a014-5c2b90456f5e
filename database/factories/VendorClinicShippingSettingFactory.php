<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Enums\ShippingType;
use App\Models\Clinic;
use App\Models\Vendor;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\VendorClinicShippingSetting>
 */
class VendorClinicShippingSettingFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $shippingType = fake()->randomElement(ShippingType::cases());

        return [
            'vendor_id' => Vendor::factory(),
            'clinic_id' => Clinic::factory(),
            'shipping_type' => $shippingType,
            'default_shipping_fee' => $shippingType === ShippingType::FlatFee
                ? fake()->numberBetween(500, 5000) // $5.00 to $50.00 in cents
                : null,
            'free_shipping_threshold' => $shippingType === ShippingType::FreeOverMinimum
                ? fake()->numberBetween(5000, 50000) // $50.00 to $500.00 in cents
                : null,
        ];
    }

    /**
     * Create a shipping setting with flat fee.
     */
    public function flatFee(?int $feeInCents = null): static
    {
        return $this->state([
            'shipping_type' => ShippingType::FlatFee,
            'default_shipping_fee' => $feeInCents ?? fake()->numberBetween(500, 5000),
            'free_shipping_threshold' => null,
        ]);
    }

    /**
     * Create a shipping setting with free shipping over minimum.
     */
    public function freeOverMinimum(?int $thresholdInCents = null, ?int $feeInCents = null): static
    {
        return $this->state([
            'shipping_type' => ShippingType::FreeOverMinimum,
            'default_shipping_fee' => $feeInCents ?? fake()->numberBetween(500, 5000),
            'free_shipping_threshold' => $thresholdInCents ?? fake()->numberBetween(5000, 50000),
        ]);
    }

    /**
     * Create a shipping setting with always free shipping.
     */
    public function alwaysFree(): static
    {
        return $this->state([
            'shipping_type' => ShippingType::AlwaysFree,
            'default_shipping_fee' => null,
            'free_shipping_threshold' => null,
        ]);
    }
}
