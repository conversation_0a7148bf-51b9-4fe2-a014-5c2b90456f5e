<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('vendor_clinic_shipping_settings', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('vendor_id')->constrained('vendors')->cascadeOnDelete();
            $table->foreignUuid('clinic_id')->constrained('clinics')->cascadeOnDelete();
            $table->string('shipping_type'); // FREE_OVER_MINIMUM, FLAT_FEE, ALWAYS_FREE
            $table->unsignedInteger('default_shipping_fee')->nullable();
            $table->unsignedInteger('free_shipping_threshold')->nullable();
            $table->timestamps();

            // Ensure unique combination of vendor and clinic
            $table->unique(['vendor_id', 'clinic_id']);
        });
    }
};
