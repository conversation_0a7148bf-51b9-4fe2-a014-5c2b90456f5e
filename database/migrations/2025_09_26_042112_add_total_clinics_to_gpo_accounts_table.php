<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('gpo_accounts', function (Blueprint $table) {
            $table->integer('total_clinics')->nullable()->after('name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('gpo_accounts', function (Blueprint $table) {
            $table->dropColumn('total_clinics');
        });
    }
};
