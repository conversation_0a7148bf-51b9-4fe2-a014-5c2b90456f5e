<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Enums\ShippingType;
use App\Models\Clinic;
use App\Models\Vendor;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Exists;

final class VendorClinicShippingSettingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->can('update', [Clinic::class, $this->route('clinic')]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'vendor_id' => [
                'required',
                'string',
                (new Exists(Vendor::class, 'id'))->where('is_enabled', true),
            ],
            'clinic_id' => [
                'required',
                'string',
                new Exists(Clinic::class, 'id'),
            ],
            'shipping_type' => [
                'required',
                'string',
                Rule::enum(ShippingType::class),
            ],
            'default_shipping_fee' => [
                'nullable',
                'integer',
                'min:0',
            ],
            'free_shipping_threshold' => [
                'nullable',
                'integer',
                'min:1',
            ],
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $shippingType = $this->get('shipping_type');

            if ($shippingType === ShippingType::AlwaysFree->value) {
                // ALWAYS_FREE: both fields must be null
                if ($this->get('default_shipping_fee') !== null) {
                    $validator->errors()->add('default_shipping_fee', 'The default shipping fee must be null when shipping type is always free.');
                }
                if ($this->get('free_shipping_threshold') !== null) {
                    $validator->errors()->add('free_shipping_threshold', 'The free shipping threshold must be null when shipping type is always free.');
                }
            } elseif ($shippingType === ShippingType::FlatFee->value) {
                // FLAT_FEE: default_shipping_fee required, free_shipping_threshold must be null
                if ($this->get('default_shipping_fee') === null) {
                    $validator->errors()->add('default_shipping_fee', 'The default shipping fee is required when shipping type is flat fee.');
                }
                if ($this->get('free_shipping_threshold') !== null) {
                    $validator->errors()->add('free_shipping_threshold', 'The free shipping threshold must be null when shipping type is flat fee.');
                }
            } elseif ($shippingType === ShippingType::FreeOverMinimum->value) {
                // FREE_OVER_MINIMUM: both fields required
                if ($this->get('default_shipping_fee') === null) {
                    $validator->errors()->add('default_shipping_fee', 'The default shipping fee is required when shipping type is free over minimum.');
                }
                if ($this->get('free_shipping_threshold') === null) {
                    $validator->errors()->add('free_shipping_threshold', 'The free shipping threshold is required when shipping type is free over minimum.');
                }
            }
        });
    }
}
