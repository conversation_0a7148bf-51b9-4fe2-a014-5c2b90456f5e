<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Http\Requests\VendorClinicShippingSettingRequest;
use App\Http\Resources\VendorClinicShippingSettingResource;
use App\Models\Clinic;
use App\Models\VendorClinicShippingSetting;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;

final class VendorClinicShippingSettingController extends Controller
{
    use AuthorizesRequests;

    public function store(
        VendorClinicShippingSettingRequest $request,
        Clinic $clinic
    ): JsonResponse {
        $this->authorize('update', $clinic);

        $validated = $request->validated();

        $shippingSetting = VendorClinicShippingSetting::updateOrCreate(
            [
                'vendor_id' => $validated['vendor_id'],
                'clinic_id' => $validated['clinic_id'],
            ],
            [
                'shipping_type' => $validated['shipping_type'],
                'default_shipping_fee' => $validated['default_shipping_fee'] ?? null,
                'free_shipping_threshold' => $validated['free_shipping_threshold'] ?? null,
            ]
        );

        return VendorClinicShippingSettingResource::make($shippingSetting)
            ->response()
            ->setStatusCode($shippingSetting->wasRecentlyCreated ? JsonResponse::HTTP_CREATED : JsonResponse::HTTP_OK);
    }

    public function show(
        Clinic $clinic,
        VendorClinicShippingSetting $vendorClinicShippingSetting
    ): JsonResponse {
        $this->authorize('view', $clinic);

        return VendorClinicShippingSettingResource::make($vendorClinicShippingSetting)
            ->response();
    }

    public function destroy(
        Clinic $clinic,
        VendorClinicShippingSetting $vendorClinicShippingSetting
    ): JsonResponse {
        $this->authorize('update', $clinic);

        $vendorClinicShippingSetting->delete();

        return response()->json(null, JsonResponse::HTTP_NO_CONTENT);
    }
}
