<?php

declare(strict_types=1);

namespace App\Http\Resources;

use App\Models\VendorClinicShippingSetting;
use App\Modules\CatalogSync\Models\CatalogSyncTask;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use Brick\Money\Money;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class Vendor extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $connection = $this->integrationConnections()?->where('clinic_id', $request->clinicId())?->first();
        $lastCatalogSync = CatalogSyncTask::query()
            ->where('integration_connection_id', $connection?->id)
            ?->latest()
            ?->first();

        $shippingTerms = $this->getShippingTerms($request->clinic());

        return [
            'id' => $this->id,
            'name' => $this->name,
            'type' => $this->type,
            'image_url' => asset("storage/{$this->image_path}"),
            'status' => $connection?->status ?? IntegrationConnectionStatus::Disconnected,
            'alert' => $connection?->alert,
            'authentication_kind' => $this->authentication_kind,
            'authentication_configuration' => $this->authentication_configuration,
            'lastProductCatalogSync' => $lastCatalogSync ? new CatalogSync($lastCatalogSync) : null,
            'integrationPoints' => $this->integration_points,
            'shippingTerms' => $shippingTerms ? [
                'cutoffTime' => $shippingTerms?->cutoff_time,
                'freeShippingThreshold' => Money::ofMinor($shippingTerms->free_shipping_threshold ?? 0, 'USD')->getAmount(),
                'shippingRate' => Money::ofMinor($shippingTerms->shipping_rate ?? 0, 'USD')->getAmount(),
                'defaultShippingFee' => Money::ofMinor($shippingTerms->default_shipping_fee ?? 0, 'USD')->getAmount(),
                'shippingType' => $shippingTerms?->shipping_type,
                'isClinicSpecific' => $shippingTerms instanceof VendorClinicShippingSetting,
            ] : null,

        ];
    }
}
