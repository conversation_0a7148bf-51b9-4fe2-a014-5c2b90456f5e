<?php

declare(strict_types=1);

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class VendorClinicShippingSettingResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'vendor_id' => $this->vendor_id,
            'clinic_id' => $this->clinic_id,
            'shipping_type' => $this->shipping_type,
            'default_shipping_fee' => $this->default_shipping_fee,
            'free_shipping_threshold' => $this->free_shipping_threshold,
            'cutoff_time' => $this->cutoff_time,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
