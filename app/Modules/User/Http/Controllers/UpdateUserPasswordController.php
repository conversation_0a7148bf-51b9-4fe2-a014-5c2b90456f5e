<?php

declare(strict_types=1);

namespace App\Modules\User\Http\Controllers;

use App\Modules\User\Actions\UpdateUserPassword;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Password;
use Illuminate\Validation\ValidationException;

final class UpdateUserPasswordController
{
    public function __construct(
        private readonly UpdateUserPassword $updateUserPassword,
    ) {}

    public function __invoke(Request $request): Response
    {
        $data = $request->validate([
            'current_password' => ['required', 'string'],
            'new_password' => ['required', 'string', Password::defaults()],
        ]);

        if (! Hash::check($data['current_password'], $request->user()->password)) {
            throw ValidationException::withMessages([
                'current_password' => ['The current password is incorrect'],
            ]);
        }

        if (Hash::check($data['new_password'], $request->user()->password)) {
            throw ValidationException::withMessages([
                'new_password' => ['Your new password must be different from your current password.'],
            ]);
        }

        $this->updateUserPassword->handle(
            $request->user(),
            $data['new_password'],
        );

        return response()->noContent();
    }
}
