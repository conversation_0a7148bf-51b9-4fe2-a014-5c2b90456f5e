<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Data;

use App\Modules\Gpo\Models\GpoAccount;
use Illuminate\Support\Collection;
use Spatie\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Lazy;

final class GpoAccountData extends Data
{
    public function __construct(
        public readonly string $id,
        public readonly string $name,
        public readonly ?int $totalClinics,
        public readonly ?string $imageUrl,
        #[DataCollectionOf(GpoPreferredVendorData::class)]
        public readonly Lazy|Collection $preferredVendors,
    ) {}

    public static function fromModel(GpoAccount $account): self
    {
        return new self(
            id: $account->id,
            name: $account->name,
            imageUrl: $account->details?->imageUrl,
            totalClinics: $account->total_clinics,
            preferredVendors: Lazy::whenLoaded('recommendedVendors',
                $account, fn () => GpoPreferredVendorData::collect($account->recommendedVendors, Collection::class)),
        );
    }
}
