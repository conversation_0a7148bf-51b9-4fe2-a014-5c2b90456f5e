<?php

declare(strict_types=1);

namespace App\Actions;

use App\Models\User;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

final class ResetUserPassword
{
    public function handle(array $data): User
    {
        $status = Password::reset(
            $data,
            function ($user) use ($data) {
                if (Hash::check($data['password'], $user->password)) {
                    throw ValidationException::withMessages([
                        'password' => ['Your new password must be different from your current password.'],
                    ]);
                }

                $user->forceFill([
                    'password' => bcrypt($data['password']),
                    'remember_token' => Str::random(60),
                ])->save();

                event(new PasswordReset($user));
            }
        );

        if ($status !== Password::PASSWORD_RESET) {
            throw ValidationException::withMessages([
                'email' => [__($status)],
            ]);
        }

        return User::with(['account', 'clinics'])->where('email', $data['email'])->firstOrFail();
    }
}
