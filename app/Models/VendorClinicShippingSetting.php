<?php

declare(strict_types=1);

namespace App\Models;

use App\Enums\ShippingType;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

final class VendorClinicShippingSetting extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'vendor_id',
        'clinic_id',
        'shipping_type',
        'default_shipping_fee',
        'free_shipping_threshold',
    ];

    /**
     * Get the vendor that owns the shipping setting.
     */
    public function vendor(): BelongsTo
    {
        return $this->belongsTo(Vendor::class);
    }

    /**
     * Get the clinic that owns the shipping setting.
     */
    public function clinic(): BelongsTo
    {
        return $this->belongsTo(Clinic::class);
    }

    /**
     * Calculate the shipping fee based on the order total.
     *
     * @param  int  $subtotal  Order subtotal in cents
     * @return array<int, int> [amount_needed_for_free_shipping, shipping_fee]
     */
    public function calculateShippingFee(int $subtotal): array
    {
        return match ($this->shipping_type) {
            ShippingType::AlwaysFree => [0, 0],
            ShippingType::FlatFee => [0, $this->default_shipping_fee],
            ShippingType::FreeOverMinimum => $subtotal >= $this->free_shipping_threshold
                ? [0, 0]
                : [$this->free_shipping_threshold - $subtotal, $this->default_shipping_fee],
        };
    }

    public function cutoffTime(): Attribute
    {
        return Attribute::get(fn () => $this->vendor->shippingTerms?->cutoff_time);
    }

    protected function casts(): array
    {
        return [
            'shipping_type' => ShippingType::class,
        ];
    }
}
