<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasVersion4Uuids as HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

final class VendorShippingTerms extends Model
{
    use HasUuids, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'cutoff_time',
        'free_shipping_threshold',
        'shipping_rate',
    ];

    /**
     * Get the vendor that the shipping details belong to.
     */
    public function vendor(): BelongsTo
    {
        return $this->belongsTo(Vendor::class);
    }

    /**
     * Calculate the shipping fee based on the order total.
     *
     * Applies free shipping if the order meets the free shipping threshold or if the
     * shipping rate is set to zero. Otherwise, returns the standard shipping rate.
     *
     * @param  int  $subtotal  Order subtotal in cents
     * @return array<int, int> [amount_needed_for_free_shipping, shipping_fee]
     */
    public function calculateShippingFee(int $subtotal): array
    {
        $freeShippingThreshold = $this->free_shipping_threshold;
        $shippingRate = $this->shipping_rate;

        if ($freeShippingThreshold === 0 && $shippingRate > 0) {
            return [0, $shippingRate];
        }

        if ($freeShippingThreshold > 0 && $subtotal >= $freeShippingThreshold) {
            return [0, 0];
        }

        return [$freeShippingThreshold - $subtotal, $shippingRate];
    }
}
