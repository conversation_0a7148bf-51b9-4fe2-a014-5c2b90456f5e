import { OfferType } from '@/types';
import { OnboardingStatus } from '@/libs/onboarding/types';
import { PROMO_TYPE } from '@/constants';

export interface Gpo {
  id: string;
  name: string;
  imageUrl: string;
}
export interface Account {
  id: string;
  gpo: Gpo | null;
  clinics: ClinicType[];
}

export interface UserType {
  id: string;
  email: string;
  role: 'ADMINISTRATOR';
  name: string;
  phoneNumber: string;
  account: Account;
  accountId: string;
  clinicId?: string;
  systemUser?: boolean;
  isImpersonating?: boolean;
}

export interface GpoUserType {
  id: string;
  email: string;
  name: string;
  account: Gpo & {
    totalClinics: number | null;
  };
  account_id: string;
}

export type AddressType = {
  addressId: string;
  street: string;
  state: string;
  city: string;
  postalCode: string;
};

export interface ClinicType {
  accountId: string;
  ein: string;
  id: string;
  name: string;
  shippingAddress: AddressType;
  billingAddress: AddressType;
  sameAddress: boolean;
  onboardingStatus: OnboardingStatus;
  hasAnyVendorConnected: boolean;
  email: string;
  status: string;
  manager: string;
}

export type ClinicStatusType = 'ACTIVE' | 'INACTIVE' | 'PROCESSING';
export type ClinicAccountType = 'SINGLE' | 'GROUP';

export interface AccountType {
  id: string;
  accountId: string;
  name: string;
  ein: string;
  address: AddressType;
  phoneNumber: string;
  onboardingStatus: OnboardingStatus;
  clinics: ClinicType[];
  hasAnyVendorConnected: boolean;
  motivatedBy: string[];
  priorities: string[];
  gpo: {
    id: string;
    imageUrl: string;
    name: string;
    preferredVendors: {
      order: number;
      vendorId: string;
    }[];
  } | null;
}

export interface RequirementType {
  type: string;
  description?: string;
  minimumQuantity: number;
  message?: string;
  freeProductOffer?: OfferType;
}

export interface BenefitType {
  type: string;
  description?: string;
  quantity: number;
  message?: string;
  freeProductOffer?: OfferType;
}

export interface PromoType {
  id: string;
  name: string;
  type: keyof typeof PROMO_TYPE;
  description: string;
  startedAt: string;
  endedAt: string;
  vendor: {
    id: string;
    name: string;
    imageUrl: string;
  };
  offers?: OfferType[];
  keyword: string;
  requirements: RequirementType[];
  benefits: BenefitType[];
}

export interface RebateType {
  id: string;
  promotion: PromoType;
  currentSpendAmount: string;
  currentRebatePercent: number;
  nextTierMinimumSpendAmountThreshold: string; // | null
  nextTierRebatePercent: number; // | null
  estimatedRebateAmount: string;
  isLastTier: boolean;
  suggestedProductOffers: OfferType[];
}
