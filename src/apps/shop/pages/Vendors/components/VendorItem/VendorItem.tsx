import type { VendorType } from '@/types';
import { Menu, UnstyledButton } from '@mantine/core';
import { FEATURE_FLAGS, MODAL_NAME } from '@/constants';
import { Button, type ButtonProps } from '@/libs/ui/Button/Button';
import { Alert } from '@/libs/ui/Alert/Alert';
import { MarkdownRenderer } from '@/libs/ui/MarkdownRenderer/MarkdownRenderer';
import { useModalStore } from '@/apps/shop/stores/useModalStore';
import { CatalogSyncStatus } from '../CatalogSyncStatus/CatalogSyncStatus';
import { VendorConnectionStatus } from '../VendorConnectionStatus/VendorConnectionStatus';
import { BetaBadge } from '../BetaBadge/BetaBadge';
import { RecommendedTag } from '@/libs/ui/RecommendedTag/RecommendedTag';
import { Icon } from '@/libs/icons/Icon';
import { mergeClasses } from '@/utils';
import { VendorPreferenceLabel } from '../VendorPreferenceLabel/VendorPreferenceLabel';
import { ClinicInfoType } from '../../../Settings/types';

interface VendorItemProps {
  clinicInfo: ClinicInfoType | null;
  vendor: VendorType;
  isPreferred: boolean;
  onShippingUpdate: VoidFunction;
}
export const VendorItem = ({
  clinicInfo,
  vendor,
  isPreferred,
  onShippingUpdate,
}: VendorItemProps) => {
  const { openModal } = useModalStore();

  const handleOpenConnectModal = (vendor: VendorType) => {
    const modalName =
      vendor.name.toLocaleLowerCase() === 'amazon'
        ? MODAL_NAME.AMAZON_VENDOR_CONNECT
        : MODAL_NAME.VENDOR_CONNECT;

    openModal({ name: modalName, vendor });
  };

  const handleOpenShippingModal = (vendor: VendorType) => {
    openModal({
      name: MODAL_NAME.SHIPPING_FEE,
      vendor,
      refetchVendor: onShippingUpdate,
    });
  };

  const {
    alert,
    imageUrl,
    name,
    lastProductCatalogSync,
    status,
    type,
    integrationPoints,
  } = vendor;

  // Check if vendor is beta: doesn't have both sync_product_catalog AND place_orders
  const isBeta =
    !integrationPoints.includes('sync_product_catalog') ||
    !integrationPoints.includes('place_orders');

  const connectCTA: {
    show: boolean;
    label: string;
    variant: ButtonProps['variant'];
  } = {
    show:
      lastProductCatalogSync?.status === 'failed' || status === 'disconnected',
    label: lastProductCatalogSync ? 'Update Credentials' : 'Connect',
    variant: lastProductCatalogSync ? 'secondary' : 'default',
  };

  const isMoreOptionsDisabled =
    status === 'connecting' ||
    (status === 'disconnected' && !lastProductCatalogSync);

  const hasNoProductCatalogSync =
    status === 'connected' &&
    !integrationPoints.includes('sync_product_catalog');

  const borderColor =
    lastProductCatalogSync?.status === 'failed'
      ? 'border-red-300'
      : isPreferred
        ? 'border-[#0072C6]'
        : 'border-black/[0.09]';

  return (
    <div
      className={mergeClasses(
        'relative flex h-full w-full flex-col rounded-sm border bg-white px-5 pt-8 pb-4',
        borderColor,
      )}
    >
      {isPreferred && (
        <RecommendedTag
          left="14rem"
          size="xs"
          placement="top"
          className="text-sxs bg-[#0072C6] py-0.5 font-bold text-white capitalize"
        >
          Preferred vendor
        </RecommendedTag>
      )}
      <div className="flex items-center">
        <div className="relative h-16 w-20 overflow-hidden rounded-sm border border-black/[0.09]">
          <img
            src={imageUrl}
            alt="Vendor Logo"
            className="absolute top-1/2 left-1/2 h-auto w-[calc(100%-8px)] -translate-x-1/2 -translate-y-1/2"
          />
        </div>
        <div className="ml-4 flex flex-col">
          <VendorPreferenceLabel clinicInfo={clinicInfo} vendor={vendor} />
          <p className="text-[16px] font-medium">
            {name} {isBeta && <BetaBadge />}
          </p>
          <div className="flex">
            <span className="text-sxs mr-1 text-black/70">Category:</span>
            <span className="text-sxs font-medium text-black capitalize">
              {type}
            </span>
          </div>
        </div>
        <Menu>
          <Menu.Target>
            <Button
              variant="unstyled"
              disabled={isMoreOptionsDisabled}
              aria-label="Vendor menu options"
              className="ml-auto"
            >
              <Icon
                name="moreOptions"
                color="#333"
                size="1.4rem"
                aria-hidden={true}
              />
            </Button>
          </Menu.Target>
          <Menu.Dropdown>
            <Menu.Label>
              <UnstyledButton onClick={() => handleOpenConnectModal(vendor)}>
                <p className="text-sm font-medium text-black">
                  Edit Credentials
                </p>
              </UnstyledButton>
            </Menu.Label>
            {status === 'connected' && FEATURE_FLAGS.SHIPPING_OVERRIDE && (
              <Menu.Label>
                <UnstyledButton onClick={() => handleOpenShippingModal(vendor)}>
                  <p className="text-sm font-medium text-black">
                    Shipping Information
                  </p>
                </UnstyledButton>
              </Menu.Label>
            )}
          </Menu.Dropdown>
        </Menu>
      </div>
      <div className="divider-h my-4"></div>
      <div className="flex items-center justify-between">
        <p className="text-sm text-black/70">Catalog Sync</p>
        <CatalogSyncStatus
          lastProductCatalogSync={lastProductCatalogSync}
          hasNoProductCatalogSync={hasNoProductCatalogSync}
        />
      </div>
      {!!alert && (
        <div className="my-2">
          <Alert type={alert.type}>
            <MarkdownRenderer markdown={alert.message} />
          </Alert>
        </div>
      )}
      <div className="divider-h my-4"></div>
      <div className="my-4 flex h-full flex-col justify-end">
        {connectCTA.show ? (
          <Button
            onClick={() => handleOpenConnectModal(vendor)}
            variant={connectCTA.variant}
          >
            {connectCTA.label}
          </Button>
        ) : (
          <VendorConnectionStatus status={status} />
        )}
      </div>
    </div>
  );
};
