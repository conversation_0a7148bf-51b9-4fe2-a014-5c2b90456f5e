import {
  ModalOptionProps,
  useModalStore,
} from '@/apps/shop/stores/useModalStore';
import { Modal } from '@/components';
import { MODAL_NAME } from '@/constants';
import styles from './ShippingFeeModal.module.css';
import { Input } from '@/libs/form/Input';
import { VendorType } from '@/types';
import { Button } from '@/libs/ui/Button/Button';
import { Select } from '@/libs/form/Select';
import { moneyMask } from '@/libs/form/masks';
import { shippingOptions, SHIPPING_OPTIONS_VALUES, SCHEMA } from './constants';
import { useEffect } from 'react';
import { fetchApi } from '@/libs/utils/api';
import { useAccountStore } from '@/apps/shop/stores/useAccountStore';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { useAsyncRequest } from '@/libs/api/hooks/useAsyncRequest/useAsyncRequest';
import { defaultFormErrorHandler, successNotification } from '@/utils';

type ShippingFeeModalOptions = ModalOptionProps & {
  vendor: VendorType;
  refetchVendor: VoidFunction;
};

export const ShippingFeeModal = () => {
  const { closeModal, modalOption } = useModalStore();
  const { activeClinic } = useAccountStore();
  const { vendor, refetchVendor } = modalOption as ShippingFeeModalOptions;

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    setError,
    watch,
    reset,
  } = useForm({
    resolver: yupResolver(SCHEMA),
    mode: 'all',
    reValidateMode: 'onChange',
    defaultValues: {
      shippingType: SHIPPING_OPTIONS_VALUES.FREE_OVER_MINIMUM,
      defaultShippingFee: '$0.00',
      freeShippingThreshold: '$0.00',
    },
  });

  const shippingType = watch('shippingType');

  useEffect(() => {
    if (vendor?.shippingTerms) {
      const { shippingTerms } = vendor;

      const defaultValues = {
        shippingType:
          shippingTerms.shippingType ||
          SHIPPING_OPTIONS_VALUES.FREE_OVER_MINIMUM,
        defaultShippingFee: moneyMask(shippingTerms.defaultShippingFee || '0'),
        freeShippingThreshold: moneyMask(
          shippingTerms.freeShippingThreshold || '0',
        ),
      };

      reset(defaultValues);
    }
  }, [vendor, reset]);

  const { apiRequest: handleUpdateShippingSettings, isLoading } =
    useAsyncRequest({
      apiFunc: handleSubmit(async (data) => {
        if (!activeClinic?.id) {
          throw new Error('No active clinic found');
        }

        const payload = {
          shippingType: data.shippingType,
          defaultShippingFee:
            data.shippingType === SHIPPING_OPTIONS_VALUES.ALWAYS_FREE
              ? null
              : // TODO: Remove 100 * when backend is updated
                100 *
                parseFloat(
                  data.defaultShippingFee?.replace(/[$,]/g, '') || '0',
                ),
          freeShippingThreshold:
            data.shippingType === SHIPPING_OPTIONS_VALUES.FREE_OVER_MINIMUM
              ? // TODO: Remove 100 * when backend is updated
                100 *
                parseFloat(
                  data.freeShippingThreshold?.replace(/[$,]/g, '') || '0',
                )
              : null,
          vendorId: vendor?.id,
          clinicId: activeClinic.id,
        };

        await fetchApi(`/clinics/${activeClinic.id}/shipping-settings`, {
          method: 'POST',
          body: payload,
        });

        refetchVendor();

        successNotification('Shipping settings saved successfully');
        closeModal();
      }),
      errorFunc: (error) => {
        defaultFormErrorHandler(error, setError);
      },
    });

  if (!vendor) {
    return null;
  }

  const showFreeShippingThreshold =
    shippingType === SHIPPING_OPTIONS_VALUES.FREE_OVER_MINIMUM;
  const showInputs = shippingType !== SHIPPING_OPTIONS_VALUES.ALWAYS_FREE;

  return (
    <Modal
      name={MODAL_NAME.SHIPPING_FEE}
      size="500px"
      customClasses={{
        header: styles.outerModalHeader,
        body: styles.outerModalBody,
        content: styles.outerModalContent,
      }}
      withCloseButton
      onClose={closeModal}
      closeOnClickOutside={false}
      closeOnEscape={false}
    >
      <h4 className="mb-2 text-[20px] font-bold text-[#333]">
        Vendor Shipping Information
      </h4>
      <p className="mb-4 text-[14px] text-[#666]">
        Enter your clinic-specific shipping rules for this vendor. Once saved,
        these will override the national shipping defaults and apply to all user
        carts.
      </p>

      <form
        className="rounded-xl border border-[#F5F5F5] bg-[#F2F8FC] px-4 py-6"
        onSubmit={handleUpdateShippingSettings}
      >
        <div className="flex gap-5">
          <div>
            <img src={vendor.imageUrl} alt={vendor.name} />
          </div>
          <div className="font-medium text-[#333]">
            <h3 className="mb-2 text-[18px]">{vendor.name}</h3>
            <p className="flex gap-1 text-[14px] font-medium capitalize">
              <span className="font-medium text-[#666]">Category:</span>
              {vendor.type}
            </p>
          </div>
        </div>
        <div className="divider-h my-4 w-full" />
        <Select
          label="Shipping Fee Type"
          options={shippingOptions}
          showEmptyOption={false}
          {...register('shippingType')}
          error={errors.shippingType?.message}
        />
        {showInputs && (
          <div className="mt-4 flex gap-4">
            <Input
              label="Default Shipping Fee"
              mask={moneyMask}
              {...register('defaultShippingFee')}
              error={errors.defaultShippingFee?.message}
            />
            {showFreeShippingThreshold && (
              <Input
                label="Free Shipping Threshold ($)"
                mask={moneyMask}
                {...register('freeShippingThreshold')}
                error={errors.freeShippingThreshold?.message}
              />
            )}
          </div>
        )}
        <Button
          variant="secondary"
          className="my-6"
          type="submit"
          disabled={!isValid}
          loading={isLoading}
        >
          Save Shipping Information
        </Button>
        <Button
          className="align-center w-full underline"
          variant="unstyled"
          type="button"
          onClick={closeModal}
        >
          Cancel
        </Button>
      </form>
    </Modal>
  );
};
