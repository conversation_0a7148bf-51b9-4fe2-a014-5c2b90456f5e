import * as yup from 'yup';

export const SHIPPING_OPTIONS_VALUES = {
  FREE_OVER_MINIMUM: 'FREE_OVER_MINIMUM',
  FLAT_FEE: 'FLAT_FEE',
  ALWAYS_FREE: 'ALWAYS_FREE',
};

export const shippingOptions = [
  {
    label: 'Free Shipping Over minimum',
    value: SHIPPING_OPTIONS_VALUES.FREE_OVER_MINIMUM,
  },
  { label: 'Flat Fee per Order', value: SHIPPING_OPTIONS_VALUES.FLAT_FEE },
  {
    label: 'Free Shipping on all orders',
    value: SHIPPING_OPTIONS_VALUES.ALWAYS_FREE,
  },
];

export const SCHEMA = yup.object().shape({
  shippingType: yup.string().required('Please select a shipping option'),
  defaultShippingFee: yup
    .string()
    .required('Please enter the shipping fee amount')
    .test(
      'is-valid-amount',
      'Please enter a valid shipping fee amount',
      (value) => {
        if (!value) return true;
        const numericValue = parseFloat(value.replace(/[$,]/g, ''));
        return !isNaN(numericValue) && numericValue >= 0;
      },
    )
    .test(
      'minimum-amount',
      'Shipping fee must be at least $1.00',
      (value, context) => {
        if (context.parent.shippingType === SHIPPING_OPTIONS_VALUES.FLAT_FEE) {
          if (!value) return false;
          const numericValue = parseFloat(value.replace(/[$,]/g, ''));
          return numericValue >= 1;
        }
        return true;
      },
    )
    .when('shippingType', {
      is: SHIPPING_OPTIONS_VALUES.FLAT_FEE,
      then: (schema) => schema.required('Please enter the shipping fee amount'),
      otherwise: (schema) => schema.optional(),
    }),
  freeShippingThreshold: yup
    .string()
    .required('Please enter the minimum order amount for free shipping')
    .test(
      'is-valid-amount',
      'Please enter a valid threshold amount',
      (value) => {
        if (!value) return true;
        const numericValue = parseFloat(value.replace(/[$,]/g, ''));
        return !isNaN(numericValue) && numericValue >= 0;
      },
    )
    .test(
      'minimum-threshold',
      'Free shipping threshold must be at least $1.00',
      (value, context) => {
        if (
          context.parent.shippingType ===
          SHIPPING_OPTIONS_VALUES.FREE_OVER_MINIMUM
        ) {
          if (!value) return false;
          const numericValue = parseFloat(value.replace(/[$,]/g, ''));
          return numericValue >= 1;
        }
        return true;
      },
    )
    .when('shippingType', {
      is: SHIPPING_OPTIONS_VALUES.FREE_OVER_MINIMUM,
      then: (schema) =>
        schema.required(
          'Please enter the minimum order amount for free shipping',
        ),
      otherwise: (schema) => schema.optional(),
    }),
});
