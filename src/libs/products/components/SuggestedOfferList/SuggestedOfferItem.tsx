import { Link } from 'react-router-dom';
import { OfferType } from '@/types';
import { Divider, Image, Modal, Text } from '@mantine/core';
import { Button } from '@/libs/ui/Button/Button';
import { getPriceString } from '@/utils';
import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';
import { useState, type FormEventHandler } from 'react';
import {
  AddToCartInput,
  type AddToCartInputProps,
} from '../AddToCartInput/AddToCartInput';
import { AddToCartButton } from '../AddToCartForm/components/AddToCartButton/AddToCartButton';
import { stockStatusConfigs } from '../StockStatusIcon/StockStatusIcon';
import { useDisclosure } from '@mantine/hooks';
import { PurchaseHistoryChart } from '../PurchaseHistory/PurchaseHistoryChart';
import { getProductUrl } from '@/apps/shop/routes/utils';
import { getProductOfferComputedData } from '@/libs/products/utils/getProductComputedData';
import { VendorSwap } from '../VendorSwap/VendorSwap';

type OfferSubstitutesItemProps = {
  offer: OfferType;
  enableVendorSwap?: boolean;
};

export const SuggestedOfferItem = ({
  offer,
  enableVendorSwap = false,
}: OfferSubstitutesItemProps) => {
  const { product } = offer;
  const [currentOffer, setCurrentOffer] = useState(offer);
  const { id, increments, vendorSku, stockStatus, vendor } = currentOffer;
  const [isModalOpen, { open, close }] = useDisclosure(false);
  const { addToCart, offersMapData } = useCartStore();
  const [amount, setAmount] = useState(increments ?? 1);

  const { isLoading = false, quantity = 0 } = offersMapData[id] ?? {};

  const handleQuantityUpdate: AddToCartInputProps['onUpdate'] = ({
    amount: newAmount,
  }) => {
    setAmount(newAmount);
  };

  const handleAddToCartClick: FormEventHandler<HTMLFormElement> = (event) => {
    event.preventDefault();
    addToCart({
      offers: [
        {
          productOfferId: id,
          quantity: quantity + amount,
        },
      ],
      onError: () => {},
    });
  };

  const handleSwapVendor = (newOfferId: string) => {
    setCurrentOffer(product.offers.find(({ id }) => newOfferId === id)!);
  };

  const productUrl = getProductUrl(product.id, id);
  const { salePrice, originalPrice } =
    getProductOfferComputedData(currentOffer);

  if (!salePrice) return null;

  return (
    <>
      <div className="relative flex w-full justify-between gap-2 rounded border-b border-black/[0.05] bg-white p-3 shadow-sm">
        <div className="relative flex w-[calc(100%-260px)] items-center gap-2">
          {enableVendorSwap && (
            <div className="w-[120px]">
              <VendorSwap
                currentOfferId={id}
                offers={product.offers}
                onSwap={(newOfferId) => {
                  handleSwapVendor(newOfferId);
                }}
              />
            </div>
          )}
          <div className="min-w-0 flex-1 flex-col">
            <Link
              to={productUrl}
              className="block w-full overflow-hidden text-ellipsis whitespace-nowrap text-[#333] hover:underline"
            >
              <h4 className="w-full max-w-full overflow-hidden text-sm leading-[1.7] font-medium text-ellipsis whitespace-nowrap">
                {product.name}
              </h4>
            </Link>
            <div className="flex items-center gap-[0.5rem]">
              {!enableVendorSwap && (
                <Image src={vendor?.imageUrl} alt={vendor?.name} h={22} />
              )}
              <Text size="xs" c="#666666CC">
                SKU:
              </Text>
              <Text size="xs" c="#333">
                {vendorSku}
              </Text>
              <Divider orientation="vertical" />
              <Text size="xs" fw={500} c="green.0">
                {stockStatusConfigs[stockStatus]?.name}
              </Text>
              <Divider orientation="vertical" />
              <Button
                variant="unstyled"
                onClick={open}
                style={{ padding: '0.375rem 0' }}
              >
                <Text size="0.75rem" c="#447BFD" fw="500">
                  Purchase History
                </Text>
              </Button>
            </div>
          </div>
        </div>
        <div className="flex min-w-[200px] items-center justify-end gap-2">
          {originalPrice > salePrice ? (
            <div className="flex flex-col items-end">
              <Text size="xs" c="#666" td="line-through">
                {getPriceString(originalPrice)}
              </Text>

              <Text size="sm" fw={500} c="#333333">
                {getPriceString(salePrice)}
              </Text>
            </div>
          ) : (
            <Text size="sm" fw={500} c="#333333">
              {getPriceString(salePrice)}
            </Text>
          )}

          <form onSubmit={handleAddToCartClick} className="flex gap-[0.5rem]">
            <div className="w-[60px]">
              <AddToCartInput
                hideButtons
                originalAmount={increments}
                minIncrement={increments}
                onUpdate={handleQuantityUpdate}
                size="sm"
              />
            </div>
            <div className="w-auto">
              <AddToCartButton
                quantityInCart={quantity}
                isLoading={isLoading}
                fullWidth
                size="sm"
                onlyIcon
              />
            </div>
          </form>
        </div>
      </div>
      <Modal
        opened={isModalOpen}
        onClose={close}
        title="Purchase History"
        size="auto"
      >
        <div className="p-4">
          <PurchaseHistoryChart productId={currentOffer.id} />
        </div>
      </Modal>
    </>
  );
};
